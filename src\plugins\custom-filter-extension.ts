/**
 * 自定义筛选图标扩展
 * 将默认的筛选图标替换为表情符号图标
 */

// 自定义筛选图标渲染器
export class CustomFilterIconRenderer {
  private static readonly NORMAL_ICON = '⬇️';
  private static readonly FILTERED_ICON = '🔻';
  
  /**
   * 渲染自定义筛选图标
   * @param ctx Canvas 2D 渲染上下文
   * @param x X 坐标
   * @param y Y 坐标
   * @param width 宽度
   * @param height 高度
   * @param isFiltered 是否已筛选状态
   */
  static renderIcon(
    ctx: CanvasRenderingContext2D,
    x: number,
    y: number,
    width: number,
    height: number,
    isFiltered: boolean = false
  ): void {
    const icon = isFiltered ? this.FILTERED_ICON : this.NORMAL_ICON;
    
    // 设置字体样式
    const fontSize = Math.min(width, height) * 0.8;
    ctx.font = `${fontSize}px Arial`;
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    
    // 计算居中位置
    const centerX = x + width / 2;
    const centerY = y + height / 2;
    
    // 绘制表情符号
    ctx.fillText(icon, centerX, centerY);
  }
}

/**
 * 自定义筛选器扩展类
 * 用于替换默认的筛选图标
 */
export class CustomFilterExtension {
  private univerAPI: any;
  
  constructor(univerAPI: any) {
    this.univerAPI = univerAPI;
  }
  
  /**
   * 初始化自定义筛选图标
   */
  init(): void {
    // 等待 DOM 渲染完成后应用自定义样式
    setTimeout(() => {
      this.applyCustomFilterIcons();
      this.setupFilterIconObserver();
    }, 1000);
  }
  
  /**
   * 应用自定义筛选图标样式
   */
  private applyCustomFilterIcons(): void {
    // 通过 CSS 隐藏原始的筛选图标并添加自定义图标
    const style = document.createElement('style');
    style.textContent = `
      /* 隐藏原始的筛选图标 */
      .univer-filter-icon,
      .univer-sheets-filter-icon,
      [class*="filter-icon"],
      [class*="FilterIcon"] {
        opacity: 0 !important;
        visibility: hidden !important;
      }
      
      /* 为筛选按钮添加自定义图标 */
      .univer-filter-button,
      .univer-sheets-filter-button,
      [class*="filter-button"],
      [class*="FilterButton"] {
        position: relative !important;
      }
      
      .univer-filter-button::after,
      .univer-sheets-filter-button::after,
      [class*="filter-button"]::after,
      [class*="FilterButton"]::after {
        content: '⬇️' !important;
        position: absolute !important;
        top: 50% !important;
        left: 50% !important;
        transform: translate(-50%, -50%) !important;
        font-size: 12px !important;
        line-height: 1 !important;
        pointer-events: none !important;
      }
      
      /* 已筛选状态的图标 */
      .univer-filter-button.filtered::after,
      .univer-sheets-filter-button.filtered::after,
      [class*="filter-button"].filtered::after,
      [class*="FilterButton"].filtered::after,
      .univer-filter-button[data-filtered="true"]::after,
      .univer-sheets-filter-button[data-filtered="true"]::after {
        content: '🔻' !important;
      }
      
      /* 通用筛选相关元素的图标替换 */
      [data-testid*="filter"]::after,
      [aria-label*="filter"]::after,
      [aria-label*="Filter"]::after {
        content: '⬇️' !important;
        position: absolute !important;
        top: 50% !important;
        left: 50% !important;
        transform: translate(-50%, -50%) !important;
        font-size: 12px !important;
        line-height: 1 !important;
        pointer-events: none !important;
      }
    `;
    
    document.head.appendChild(style);
  }
  
  /**
   * 设置筛选图标观察器
   * 监听 DOM 变化，动态应用自定义图标
   */
  private setupFilterIconObserver(): void {
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList') {
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              const element = node as Element;
              this.processFilterElements(element);
            }
          });
        }
      });
    });
    
    // 开始观察
    observer.observe(document.body, {
      childList: true,
      subtree: true
    });
    
    // 处理已存在的元素
    this.processFilterElements(document.body);
  }
  
  /**
   * 处理筛选相关元素
   */
  private processFilterElements(container: Element): void {
    // 查找所有可能的筛选按钮元素
    const filterSelectors = [
      '[class*="filter"]',
      '[class*="Filter"]',
      '[data-testid*="filter"]',
      '[aria-label*="filter"]',
      '[aria-label*="Filter"]',
      'button[title*="filter"]',
      'button[title*="Filter"]'
    ];
    
    filterSelectors.forEach(selector => {
      const elements = container.querySelectorAll(selector);
      elements.forEach(element => {
        this.customizeFilterElement(element as HTMLElement);
      });
    });
  }
  
  /**
   * 自定义筛选元素
   */
  private customizeFilterElement(element: HTMLElement): void {
    // 添加自定义类名以便样式应用
    element.classList.add('custom-filter-element');
    
    // 监听筛选状态变化
    const observer = new MutationObserver(() => {
      this.updateFilterIcon(element);
    });
    
    observer.observe(element, {
      attributes: true,
      attributeFilter: ['class', 'data-filtered', 'aria-pressed']
    });
    
    // 初始化图标状态
    this.updateFilterIcon(element);
  }
  
  /**
   * 更新筛选图标状态
   */
  private updateFilterIcon(element: HTMLElement): void {
    // 检查是否为已筛选状态
    const isFiltered = 
      element.classList.contains('filtered') ||
      element.getAttribute('data-filtered') === 'true' ||
      element.getAttribute('aria-pressed') === 'true';
    
    if (isFiltered) {
      element.classList.add('filtered');
    } else {
      element.classList.remove('filtered');
    }
  }
}

/**
 * 创建自定义筛选预设
 * 这个函数返回一个修改过的筛选预设，包含自定义图标功能
 */
export function createCustomFilterPreset() {
  return {
    // 保持原有的筛选功能
    ...require('@univerjs/preset-sheets-filter').UniverSheetsFilterPreset(),
    
    // 添加自定义初始化逻辑
    onRendered: (univerAPI: any) => {
      const customExtension = new CustomFilterExtension(univerAPI);
      customExtension.init();
    }
  };
}
